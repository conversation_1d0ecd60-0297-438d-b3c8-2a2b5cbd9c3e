<project xmlns="http://maven.apache.org/POM/4.0.0"
	xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
	xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 https://maven.apache.org/xsd/maven-4.0.0.xsd">
	<modelVersion>4.0.0</modelVersion>
	<parent>
		<groupId>org.springframework.boot</groupId>
		<artifactId>spring-boot-starter-parent</artifactId>
		<version>2.2.1.RELEASE</version>
		<relativePath />
	</parent>
	<artifactId>com.adins.esign.coverage</artifactId>
	<version>4.13.3</version>
	<packaging>pom</packaging>

	<dependencies>
		<dependency>
			<groupId>org.springframework.boot</groupId>
			<artifactId>com.adins.esign.cms</artifactId>
			<version>${project.version}</version>
			<scope>compile</scope>
		</dependency>
		<dependency>
			<groupId>org.springframework.boot</groupId>
			<artifactId>com.adins.esign.model</artifactId>
			<version>${project.version}</version>
			<exclusions>
				<exclusion>
					<groupId>org.hibernate.javax.persistence</groupId>
					<artifactId>hibernate-jpa-2.0-api</artifactId>
				</exclusion>
			</exclusions>
			<scope>compile</scope>
		</dependency>
	</dependencies>

	<scm>
		<connection>scm:svn:https://mss-webdev-svr.ad-ins.com/svn/eSign/branches/parent/coverage</connection>
		<developerConnection>scm:svn:https://mss-webdev-svr.ad-ins.com/svn/eSign/branches/parent/coverage</developerConnection>
		<url>https://mss-webdev-svr.ad-ins.com/svn/eSign/branches/parent/coverage</url>
	</scm>

	<build>
		<plugins>
			<plugin>
				<groupId>org.jacoco</groupId>
				<artifactId>jacoco-maven-plugin</artifactId>
			    <version>0.8.5</version>
				<executions>
					<execution>
						<id>report</id>
						<goals>
							<goal>report-aggregate</goal>
						</goals>
						<phase>verify</phase>
					</execution>
				</executions>
			</plugin>
		</plugins>
	</build>
</project>