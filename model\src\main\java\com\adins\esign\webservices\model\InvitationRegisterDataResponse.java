package com.adins.esign.webservices.model;

import com.adins.esign.model.custom.UserBean;
import com.adins.framework.service.base.model.MssResponseType;

public class InvitationRegisterDataResponse extends MssResponseType {
	
	private static final long serialVersionUID = 1L;
	private UserBean userData;
	private String tenantCode;
	private String vendorCode;
	private String vendorName;
	private String verifPhone;
	private String livenessUrl;
	private String useSyncPrivyVerif;
	
	public UserBean getUserData() {
		return userData;
	}
	public void setUserData(UserBean userData) {
		this.userData = userData;
	}
	public String getTenantCode() {
		return tenantCode;
	}
	public void setTenantCode(String tenantCode) {
		this.tenantCode = tenantCode;
	}
	public String getVendorCode() {
		return vendorCode;
	}
	public void setVendorCode(String vendorCode) {
		this.vendorCode = vendorCode;
	}
	public String getVendorName() {
		return vendorName;
	}
	public void setVendorName(String vendorName) {
		this.vendorName = vendorName;
	}
	public String getVerifPhone() {
		return verifPhone;
	}
	public void setVerifPhone(String verifPhone) {
		this.verifPhone = verifPhone;
	}
	public String getLivenessUrl() {
		return livenessUrl;
	}
	public void setLivenessUrl(String livenessUrl) {
		this.livenessUrl = livenessUrl;
	}
	public String getUseSyncPrivyVerif() {
		return this.useSyncPrivyVerif;
	}
	public void setUseSyncPrivyVerif(String useSyncPrivyVerif) {
		this.useSyncPrivyVerif = useSyncPrivyVerif;
	}
	
}
