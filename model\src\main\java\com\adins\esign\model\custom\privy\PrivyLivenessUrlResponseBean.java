package com.adins.esign.model.custom.privy;

import com.google.gson.annotations.SerializedName;

public class PrivyLivenessUrlResponseBean {
	
	private String id;
	@SerializedName("application_id") private String applicationId;
	@SerializedName("application_name") private String applicationName;
	@SerializedName("user_landing_url") private String userLandingUrl;
	@SerializedName("csrf_token") private String csrfToken;
	@SerializedName("expired_at") private String expiredAt;

	public String getId() {
		return this.id;
	}

	public void setId(String id) {
		this.id = id;
	}

	public String getApplicationId() {
		return this.applicationId;
	}

	public void setApplicationId(String applicationId) {
		this.applicationId = applicationId;
	}

	public String getApplicationName() {
		return this.applicationName;
	}

	public void setApplicationName(String applicationName) {
		this.applicationName = applicationName;
	}

	public String getUserLandingUrl() {
		return this.userLandingUrl;
	}

	public void setUserLandingUrl(String userLandingUrl) {
		this.userLandingUrl = userLandingUrl;
	}

	public String getCsrfToken() {
		return this.csrfToken;
	}

	public void setCsrfToken(String csrfToken) {
		this.csrfToken = csrfToken;
	}

	public String getExpiredAt() {
		return this.expiredAt;
	}

	public void setExpiredAt(String expiredAt) {
		this.expiredAt = expiredAt;
	}
	
}
