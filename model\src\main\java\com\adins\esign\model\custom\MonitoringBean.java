package com.adins.esign.model.custom;

import java.io.Serializable;

public class MonitoringBean implements Serializable {
	private static final long serialVersionUID = 1L;
	private String nomorDokumen;
	private String tanggalDokumen;
	private String namaDokumen;
	private String jenisDokumen;
	private String tipeDokumen;
	private String nominalDokumen;
	private String templateDokumen;
	private String hasilStamping;
	private String noSN;
	private String prosesMaterai;
	private String noIdentitas;
	private String namaIdentitas;
	private String errorMessage;
	private String nomorDokumenEncrypt;
	private String noSNEncrypt;
	private String taxType;
	private String cabang;
	private String isSuccess;
	private String encryptedDocId;
	private String documentId;
	private String documentArchiveStatus;

	public String getNomorDokumen() {
		return nomorDokumen;
	}
	public void setNomorDokumen(String nomorDokumen) {
		this.nomorDokumen = nomorDokumen;
	}
	public String getTanggalDokumen() {
		return tanggalDokumen;
	}
	public void setTanggalDokumen(String tanggalDokumen) {
		this.tanggalDokumen = tanggalDokumen;
	}
	public String getNamaDokumen() {
		return namaDokumen;
	}
	public void setNamaDokumen(String namaDokumen) {
		this.namaDokumen = namaDokumen;
	}
	public String getJenisDokumen() {
		return jenisDokumen;
	}
	public void setJenisDokumen(String jenisDokumen) {
		this.jenisDokumen = jenisDokumen;
	}
	public String getTipeDokumen() {
		return tipeDokumen;
	}
	public void setTipeDokumen(String tipeDokumen) {
		this.tipeDokumen = tipeDokumen;
	}
	public String getNominalDokumen() {
		return nominalDokumen;
	}
	public void setNominalDokumen(String nominalDokumen) {
		this.nominalDokumen = nominalDokumen;
	}
	public String getTemplateDokumen() {
		return templateDokumen;
	}
	public void setTemplateDokumen(String templateDokumen) {
		this.templateDokumen = templateDokumen;
	}
	public String getHasilStamping() {
		return hasilStamping;
	}
	public void setHasilStamping(String hasilStamping) {
		this.hasilStamping = hasilStamping;
	}
	public String getNoSN() {
		return noSN;
	}
	public void setNoSN(String noSN) {
		this.noSN = noSN;
	}
	public String getProsesMaterai() {
		return prosesMaterai;
	}
	public void setProsesMaterai(String prosesMaterai) {
		this.prosesMaterai = prosesMaterai;
	}
	public String getNoIdentitas() {
		return noIdentitas;
	}
	public void setNoIdentitas(String noIdentitas) {
		this.noIdentitas = noIdentitas;
	}
	public String getNamaIdentitas() {
		return namaIdentitas;
	}
	public void setNamaIdentitas(String namaIdentitas) {
		this.namaIdentitas = namaIdentitas;
	}
	public String getErrorMessage() {
		return errorMessage;
	}
	public void setErrorMessage(String errorMessage) {
		this.errorMessage = errorMessage;
	}
	public String getNomorDokumenEncrypt() {
		return nomorDokumenEncrypt;
	}
	public void setNomorDokumenEncrypt(String nomorDokumenEncrypt) {
		this.nomorDokumenEncrypt = nomorDokumenEncrypt;
	}
	public String getNoSNEncrypt() {
		return noSNEncrypt;
	}
	public void setNoSNEncrypt(String noSNEncrypt) {
		this.noSNEncrypt = noSNEncrypt;
	}
	public String getTaxType() {
		return taxType;
	}
	public void setTaxType(String taxType) {
		this.taxType = taxType;
	}
	public String getCabang() {
		return cabang;
	}
	public void setCabang(String cabang) {
		this.cabang = cabang;
	}
	public String getIsSuccess() {
		return isSuccess;
	}
	public void setIsSuccess(String isSuccess) {
		this.isSuccess = isSuccess;
	}
	public String getEncryptedDocId() {
		return encryptedDocId;
	}
	public void setEncryptedDocId(String encryptedDocId) {
		this.encryptedDocId = encryptedDocId;
	}
	public String getDocumentId() {
		return documentId;
	}
	public void setDocumentId(String documentId) {
		this.documentId = documentId;
	}
	public String getDocumentArchiveStatus() {
		return documentArchiveStatus;
	}
	public void setDocumentArchiveStatus(String documentArchiveStatus) {
		this.documentArchiveStatus = documentArchiveStatus;
	}
}
